.collections-container {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.collection-section {
  width: 90%;
  margin: auto;
}

.collection-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #ebdcdc;
  background-color: #041b4b;
  text-align: center;
  width: 30%;
  margin: auto;
  border-radius: 20px;
  
}

.products-grid {
  display: flex;
  flex-direction: row;
  gap: 3rem;
  overflow-x: auto;
  padding-bottom: 1rem;
  scroll-snap-type: x mandatory;
  margin-top: 3rem;
  
}

.product-card {
  min-width: 00px;
  flex: 0 0 auto;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
  transform: translateY(0);
  scroll-snap-align: start;
  
  
}

.product-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-0.5rem);
}

.product-image-container {
  position: relative;
  overflow: hidden;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  width: 300px;
  background-color: #041b4b;
  
}

.sale-badge {
  position: absolute;
  top: 0.75rem;
  left: 0.75rem;
  background-color: #dc2626;
  color: white;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  font-weight: bold;
  border-radius: 9999px;
  z-index: 10;
}

.product-image {
  width: 100%;
  height: 20rem;
  object-fit: cover;
  transition: transform 0.3s ease;
  
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-info {
  padding: 1.5rem;
  
}

.product-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
  
}

.product-card:hover .product-name {
  color: #dc2626;
}

.product-pricing {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.current-price {
  font-size: 1.25rem;
  font-weight: bold;
  color: #dc2626;
}

.original-price {
  font-size: 0.875rem;
  color: #6b7280;
  text-decoration: line-through;
}



