const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const CartItem = sequelize.define('CartItem', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  cartId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'carts',
      key: 'id'
    }
  },
  productId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    validate: {
      min: 1
    }
  },
  unitPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  totalPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  selectedColor: {
    type: DataTypes.STRING,
    allowNull: true
  },
  selectedSize: {
    type: DataTypes.STRING,
    allowNull: true
  },
  productVariant: {
    type: DataTypes.JSON,
    allowNull: true
  },
  addedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'cart_items',
  timestamps: true,
  hooks: {
    beforeCreate: (cartItem) => {
      cartItem.totalPrice = cartItem.quantity * cartItem.unitPrice;
    },
    beforeUpdate: (cartItem) => {
      if (cartItem.changed('quantity') || cartItem.changed('unitPrice')) {
        cartItem.totalPrice = cartItem.quantity * cartItem.unitPrice;
      }
    }
  }
});

module.exports = CartItem;
