const { models } = require('../config');
const { generateToken } = require('../utils/jwt');
const { validateRequest, registerSchema, loginSchema } = require('../utils/validation');

const register = async (req, res) => {
  try {
    const { firstName, lastName, email, telephone, password, dateOfBirth, gender, address, city, country, postalCode } = req.body;

    // Check if user already exists
    const { Op } = require('sequelize');
    const existingUser = await models.User.findOne({
      where: {
        [Op.or]: [{ email }, { telephone }]
      }
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'User with this email or telephone already exists'
      });
    }

    // Create new user
    const user = await models.User.create({
      firstName,
      lastName,
      email,
      telephone,
      password,
      dateOfBirth,
      gender,
      address,
      city,
      country,
      postalCode
    });

    // Create cart for user
    await models.Cart.create({
      userId: user.id
    });

    // Generate token
    const token = generateToken({ userId: user.id, email: user.email });

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user,
        token
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: error.message
    });
  }
};

const login = async (req, res) => {
  try {
    const { telephone, password } = req.body;

    // Find user by telephone
    const user = await models.User.findOne({
      where: { telephone, isActive: true }
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Update last login
    await user.update({ lastLogin: new Date() });

    // Generate token
    const token = generateToken({ userId: user.id, email: user.email });

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user,
        token
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: error.message
    });
  }
};

const getProfile = async (req, res) => {
  try {
    const user = await models.User.findByPk(req.user.id, {
      include: [
        {
          model: models.Cart,
          as: 'cart',
          include: [
            {
              model: models.CartItem,
              as: 'items',
              include: [
                {
                  model: models.Product,
                  as: 'product'
                }
              ]
            }
          ]
        }
      ]
    });

    res.json({
      success: true,
      data: { user }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get profile',
      error: error.message
    });
  }
};

const updateProfile = async (req, res) => {
  try {
    const { firstName, lastName, email, telephone, dateOfBirth, gender, address, city, country, postalCode } = req.body;

    // Check if email or telephone is being changed and already exists
    if (email || telephone) {
      const existingUser = await models.User.findOne({
        where: {
          [Op.or]: [
            ...(email ? [{ email }] : []),
            ...(telephone ? [{ telephone }] : [])
          ],
          id: { [Op.ne]: req.user.id }
        }
      });

      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: 'Email or telephone already exists'
        });
      }
    }

    // Update user
    await req.user.update({
      ...(firstName && { firstName }),
      ...(lastName && { lastName }),
      ...(email && { email }),
      ...(telephone && { telephone }),
      ...(dateOfBirth && { dateOfBirth }),
      ...(gender && { gender }),
      ...(address && { address }),
      ...(city && { city }),
      ...(country && { country }),
      ...(postalCode && { postalCode })
    });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { user: req.user }
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update profile',
      error: error.message
    });
  }
};

const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Verify current password
    const isCurrentPasswordValid = await req.user.comparePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Update password
    await req.user.update({ password: newPassword });

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to change password',
      error: error.message
    });
  }
};

module.exports = {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword
};
