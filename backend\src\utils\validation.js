const Joi = require('joi');

const registerSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  telephone: Joi.string().min(10).max(15).required(),
  password: Joi.string().min(6).max(100).required(),
  confirmPassword: Joi.string().valid(Joi.ref('password')).required(),
  dateOfBirth: Joi.date().optional(),
  gender: Joi.string().valid('male', 'female', 'other').optional(),
  address: Joi.string().optional(),
  city: Joi.string().optional(),
  country: Joi.string().optional(),
  postalCode: Joi.string().optional()
});

const loginSchema = Joi.object({
  telephone: Joi.string().required(),
  password: Joi.string().required()
});

const updateProfileSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).optional(),
  lastName: Joi.string().min(2).max(50).optional(),
  email: Joi.string().email().optional(),
  telephone: Joi.string().min(10).max(15).optional(),
  dateOfBirth: Joi.date().optional(),
  gender: Joi.string().valid('male', 'female', 'other').optional(),
  address: Joi.string().optional(),
  city: Joi.string().optional(),
  country: Joi.string().optional(),
  postalCode: Joi.string().optional()
});

const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string().min(6).max(100).required(),
  confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required()
});

const productSchema = Joi.object({
  name: Joi.string().min(2).max(200).required(),
  description: Joi.string().optional(),
  shortDescription: Joi.string().max(500).optional(),
  originalPrice: Joi.number().min(0).required(),
  salePrice: Joi.number().min(0).optional(),
  categoryId: Joi.string().uuid().required(),
  brand: Joi.string().optional(),
  colors: Joi.array().items(Joi.string()).optional(),
  sizes: Joi.array().items(Joi.string()).optional(),
  materials: Joi.array().items(Joi.string()).optional(),
  stockQuantity: Joi.number().integer().min(0).required(),
  weight: Joi.number().min(0).optional(),
  isActive: Joi.boolean().optional(),
  isFeatured: Joi.boolean().optional(),
  tags: Joi.array().items(Joi.string()).optional()
});

const categorySchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().optional(),
  parentId: Joi.string().uuid().optional(),
  gender: Joi.string().valid('men', 'women', 'boys', 'girls', 'unisex').optional(),
  type: Joi.string().valid('clothing', 'shoes', 'accessories').required(),
  isActive: Joi.boolean().optional(),
  sortOrder: Joi.number().integer().optional(),
  imageUrl: Joi.string().uri().optional()
});

const validateRequest = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }
    next();
  };
};

module.exports = {
  registerSchema,
  loginSchema,
  updateProfileSchema,
  changePasswordSchema,
  productSchema,
  categorySchema,
  validateRequest
};
