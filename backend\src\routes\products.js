const express = require('express');
const router = express.Router();

const productController = require('../controllers/productController');
const { optionalAuth } = require('../middleware/auth');

// Public routes
router.get('/', optionalAuth, productController.getAllProducts);
router.get('/featured', productController.getFeaturedProducts);
router.get('/sale', productController.getSaleProducts);
router.get('/slug/:slug', optionalAuth, productController.getProductBySlug);
router.get('/:id', optionalAuth, productController.getProductById);

module.exports = router;
