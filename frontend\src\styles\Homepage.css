 .homepage-container {
  width: 100%;
  min-height: 100vh;
  background: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.header {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  max-width: 1280px;
  padding-left: 1rem;
  padding-right: 1rem;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-bg {
  background: rgb(227, 50, 50);
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
}

.logo-text {
  font-weight: bold;
  font-size: 2.5rem;
  color: #5953a5;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-star {
  color: #5953a5;
  margin-right: 0.25rem;
  display: inline-block;
   font-size: 2.5rem;
}

.header-right {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.header-row {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
  justify-content: flex-end;
}

.search-input {
  border: 2px solid #d1d5db;
  border-radius: 0.5rem;
  padding: 0.5rem 0.5rem;
  outline: none;
  background: #fff;
  width: 150px;
  font-size: 14px;

}

.icons-area {
  display: flex;
  gap: 1rem;
  background: white;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  align-items: center;
  font-size: 18px;
}

.shopping-bag-rel {
  position: relative;
}

.shopping-bag-badge {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background: #dc2626;
  color: #fff;
  border-radius: 9999px;
  padding: 0 0.25rem;
  font-size: 0.75rem;
  font-weight: bold;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-link, .register-link {
  font-weight: bold;
  cursor: pointer;
  margin-left: 1rem;
  text-decoration: none;
  color: #374151;
  font-size: 20px;
  transition: color 0.2s ease;
  text-decoration: underline;
  font-weight: bold;
}

.login-link:hover, .register-link:hover {
  text-decoration: underline;
  color: #1f2937;
}

.category-nav {
  display: flex;
  flex-direction: row;
  gap: 2rem;
  margin-top: 0.5rem;
  margin-bottom: 2rem;
  max-width: 768px;
  width: 100%;
  justify-content: center;
  position: relative;
  padding: 16px 20px;
  background-color: #f3eae8;
  border-bottom: 1px solid #e5e5e5;
  
}

.category-group {
  position: relative;
 
}

.category-btn {
  min-width: 120px;
  padding: 1rem 1.5rem ;
  border-radius: 0.5rem;
  font-weight: bold;
  font-size: 1rem;
  border: 2px solid transparent;
  transition: all 0.5s;
  outline: none;
  background: white;
  color: black;
  opacity: 0.8;
  border-color: rgb(140, 140, 175);
  opacity: 1;
  border-radius: 10px;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  
}

.category-btn:hover {
  border-color: #ef4444;
  transform: translateY(-2px);
}

.category-group.open .category-btn {
  border-color: #ef4444;
  background-color: #fef2f2;
}


.dropdown {
  display: none;
  position: absolute;
  left: 50%;
  top: 80%;
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 0.5rem;
  z-index: 1000;
  min-width: 200px;
  padding: 1.25rem 1.5rem;
  margin-top: 1rem;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  background-color: white;
  transform: translateX(-50%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
}

.category-group.open .dropdown {
  opacity: 1;
  visibility: visible;
  display: block;
}

.dropdown a {
  color: black;
  text-decoration: none;
  transition: background-color 0.2s ease;
  font-family: "Times New Roman", Times, serif;
  display: block;
  padding: 12px 16px;
  color: #374151;
  font-size: 20px;
}

.dropdown a:hover {
  color: #5953a5;   
  background-color: #f9fafb;      
}

.dropdown ul {
  list-style: none;
  padding: 0;
  margin: 0;
  
 
}

.dropdown li {
  font-size: 20px;
  font-weight: 800;
  margin-bottom: 0.75rem solid #f3f4f6;
  border-bottom: 1px solid #f3f4f6;
  
}

.dropdown li:last-child {
  border-bottom: none;
}

.Banner {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 1px;
  padding: 0.5px;

  
}

.Banner img {
  width: 90%;
  height: 500px;
  border-radius: 1rem;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  object-fit: cover;
  margin-bottom: 100px;
} 
  @media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 16px;
  }
  
  .category-nav {
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .search-input {
    width: 150px;
  }
  
  .icons-area {
    gap: 12px;
  }
}


  
