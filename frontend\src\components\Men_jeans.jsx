import React, { useState } from 'react';
import { Heart, Bell, ShoppingBag } from 'lucide-react';
import { <PERSON> } from "react-router-dom";
import '../styles/Women_T_shirt.css';
import Footer from '../components/Footer';

import Men_jean1 from '../assets/men_jean1.jpg';
import Men_jean2 from '../assets/men_jean2.jpg';
import Men_jean3 from '../assets/men_jean3.jpg';
import Men_jean4 from '../assets/men_jean4.jpg';
import Men_jean5 from '../assets/men_jean5.jpg';
import Men_jean6 from '../assets/men_jean6.jpg';
import Men_jean7 from '../assets/men_jean7.jpg';
import Men_jean8 from '../assets/men_jean8.jpg';
import Men_jean9 from '../assets/men_jean9.jpg';
import Men_jean10 from '../assets/men_jean10.jpg';

const products = [
  { id: 1, name: "Slim Fit Jeans", originalPrice: 59.99, salePrice: 17.99, discount: 70, image: Men_jean1, colors: ['blue'] },
  { id: 2, name: "Straight Fit Jeans", originalPrice: 64.99, salePrice: 19.49, discount: 70, image: Men_jean2, colors: ['black'] },
  { id: 3, name: "Tapered Fit Jeans", originalPrice: 69.99, salePrice: 20.99, discount: 70, image: Men_jean3, colors: ['grey'] },
  { id: 4, name: "Regular Fit Jeans", originalPrice: 55.99, salePrice: 16.79, discount: 70, image: Men_jean4, colors: ['dark blue'] },
  { id: 5, name: "Relaxed Fit Jeans", originalPrice: 60.99, salePrice: 18.29, discount: 70, image: Men_jean5, colors: ['light blue'] },
  { id: 6, name: "Skinny Fit Jeans", originalPrice: 62.99, salePrice: 18.89, discount: 70, image: Men_jean6, colors: ['blue', 'black'] },
  { id: 7, name: "Distressed Jeans", originalPrice: 66.99, salePrice: 20.09, discount: 70, image: Men_jean7, colors: ['ripped blue'] },
  { id: 8, name: "Cropped Jeans", originalPrice: 58.99, salePrice: 17.69, discount: 70, image: Men_jean8, colors: ['blue'] },
  { id: 9, name: "Bootcut Jeans", originalPrice: 61.99, salePrice: 18.59, discount: 70, image: Men_jean9, colors: ['dark grey'] },
  { id: 10, name: "Baggy Fit Jeans", originalPrice: 67.99, salePrice: 20.39, discount: 70, image: Men_jean10, colors: ['blue', 'grey'] },
];


const categories = [
  {
    name: "Women",
    items: [
      { name: "T-shirt", link: "/t-shirt" },
      { name: "Shirt", link: "/shirt" },
      { name: "Jacket", link: "/jacket" },
      { name: "Skirt", link: "/skirt" },
      { name: "Shorts", link: "/shorts" },
      { name: "Jeans", link: "/jeans" },
      { name: "Trouser", link: "/trouser" },
      { name: "Dress", link: "/dress" },
      { name: "Shoes", link: "/shoes"},
    ],
  },
  {
    name: "Men",
    items: [
      { name: "T-shirt", link: "/men/t-shirt" },
      { name: "Jeans", link: "/men/jeans" },
      { name: "Jacket", link: "/men/jacket" },
      { name: "Trouser", link: "/men/trouser" },
      { name: "Shirt", link: "/men/shirt" },
      { name: "Shoes", link: "/men/shoes"},
    ],
  },
  {
    name: "Girls",
    items: [
      { name: "Clothing", link: "/girls/clothing" },
      { name: "Shoes", link: "/girls/shoes"},
    ],
  },
  {
    name: "Boys",
    items: [
      { name: "Clothing", link: "/boys/clothing" },
      { name: "Shoes", link: "/boys/shoes"},
    ],
  },
  {
    name: "Accessories",
    items: [
      { name: "Glasses", link: "/accessories/glasses" },
      { name: "Watches", link: "/accessories/watches"},
      { name: "Gloves", link: "/accessories/gloves" },
      { name: "Belt", link: "/accessories/belt" },
      { name: "Hat", link: "/accessories/hat" },
      { name: "Bag", link: "/accessories/bag" },
      { name: "Wallet", link: "/accessories/wallet" },
    ],
  },
];



const Men_jeans = () => {
  const [favorites, setFavorites] = useState(new Set());
  const [openDropdown, setOpenDropdown] = useState(null);

  const toggleFavorite = (productId) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(productId)) {
      newFavorites.delete(productId);
    } else {
      newFavorites.add(productId);
    }
    setFavorites(newFavorites);
  };

  return (
    <div>
      <div className="homepage-container">
        <div className="header">
          <div className="logo-container">
            <Link to="/" className="logo-bg">
              <span className="logo-text">
                <span className="logo-star">★</span>
                StyleStore
              </span>
            </Link>
          </div>
          <div className="header-right">
            <div className="header-row">
              <input
                type="text"
                placeholder="Search"
                className="search-input"
              />
              <div className="icons-area">
                <Bell size={20} className="text-black" />
                <Heart size={20} className="text-black" />
                <div className="shopping-bag-rel">
                  <ShoppingBag size={22} className="text-black" />
                  <span className="shopping-bag-badge">0</span>
                </div>
                <Link to="/login" className="login-link">LOGIN</Link>
                <Link to="/register" className="register-link">REGISTER</Link>
              </div>
            </div>
          </div>
        </div>
        <div className="category-nav">
          {categories.map((cat) => (
            <div
              key={cat.name}
              className={`category-group${openDropdown === cat.name ? " open" : ""}`}
              onMouseEnter={() => cat.items && setOpenDropdown(cat.name)}
              onMouseLeave={() => cat.items && setOpenDropdown(null)}
            >
              <button
                className={`category-btn${cat.name === "Women" ? " women" : ""}`}
                style={{ fontWeight: 700 }}
                onClick={() => setOpenDropdown(cat.name === openDropdown ? null : cat.name)}
                type="button"
              >
                {cat.name}
              </button>
              {cat.items && (
                <div className="dropdown">
                  <ul>
                    {cat.items.map((item) => (
                      <li key={item.name}>
                        <Link to={item.link}>{item.name}</Link>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
        <div className="women-tshirt-container">
        <div className="sale-banner">
          <div className="banner-content">
            <div className="sale-text">
              <span className="up-to">UP TO</span>
              <span className="percentage">70% OFF</span>
              <span className="sale-subtitle">ON MID-YEAR SALE</span>
            </div>
          </div>
        </div>
        <div className="women-tshirt-title">Jeans</div>

        {/* Product Grid */}
        <div className="products-grids">
          {products.map((product) => (
            <div key={product.id} className="product-cards">
              <div className="product-image-containers">
                <div className="discount-badge">{product.discount}%</div>
                <button
                  className={`favorite-btn ${favorites.has(product.id) ? 'active' : ''}`}
                  onClick={() => toggleFavorite(product.id)}
                >
                  <Heart size={16} fill={favorites.has(product.id) ? '#ef4444' : 'none'} />
                </button>
                <img src={product.image} alt={product.name} className="product-images" />
              </div>
              <div className="product-info">
                <h3 className="product-name">{product.name}</h3>
                <div className="price-container">
                  <span className="sale-price">US ${product.salePrice}</span>
                  <span className="original-price">US ${product.originalPrice}</span>
                </div>
                <div className="color-options">
                  {product.colors.map((color, index) => (
                    <div key={index} className={`color-dot ${color}`}></div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      </div>
      <div className="Footer"><Footer /></div>


      
    </div>
  );
};

export default Men_jeans;