import React, { useState } from 'react';
import { Heart, Bell, ShoppingBag } from 'lucide-react';
import { <PERSON> } from "react-router-dom";
import '../styles/Women_T_shirt.css';
import Footer from '../components/Footer';

import Men_trouser1 from '../assets/men_trouser1.jpg';
import Men_trouser2 from '../assets/men_trouser2.webp';
import Men_trouser3 from '../assets/men_trouser3.webp';
import Men_trouser4 from '../assets/men_trouser4.webp';
import Men_trouser5 from '../assets/men_trouser5.webp';
import Men_trouser6 from '../assets/men_trouser6.webp';
import Men_trouser7 from '../assets/men_trouser7.webp';
import Men_trouser8 from '../assets/men_trouser8.webp';
import Men_trouser9 from '../assets/men_trouser9.webp';


const products = [
  { id: 1, name: "Slim Fit Chinos", originalPrice: 59.99, salePrice: 17.99, discount: 70, image: Men_trouser1, colors: ['khaki', 'navy'] },
  { id: 2, name: "Classic Black Trousers", originalPrice: 69.99, salePrice: 20.99, discount: 70, image: Men_trouser2, colors: ['black'] },
  { id: 3, name: "Stretch Fit Jeans", originalPrice: 79.99, salePrice: 23.99, discount: 70, image: Men_trouser3, colors: ['blue'] },
  { id: 4, name: "Cotton Cargo Pants", originalPrice: 64.99, salePrice: 19.49, discount: 70, image: Men_trouser4, colors: ['olive'] },
  { id: 5, name: "Tapered Formal Trousers", originalPrice: 89.99, salePrice: 26.99, discount: 70, image: Men_trouser5, colors: ['grey', 'black'] },
  { id: 6, name: "Urban Jogger Pants", originalPrice: 49.99, salePrice: 14.99, discount: 70, image: Men_trouser6, colors: ['black', 'white'] },
  { id: 7, name: "Relaxed Fit Linen Trousers", originalPrice: 74.99, salePrice: 22.49, discount: 70, image: Men_trouser7, colors: ['beige'] },
  { id: 8, name: "Vintage Washed Jeans", originalPrice: 84.99, salePrice: 25.49, discount: 70, image: Men_trouser8, colors: ['blue'] },
  { id: 9, name: "Checkered Smart Trousers", originalPrice: 69.99, salePrice: 20.99, discount: 70, image: Men_trouser9, colors: ['grey', 'brown'] }
];


const categories = [
  {
    name: "Women",
    items: [
      { name: "T-shirt", link: "/t-shirt" },
      { name: "Shirt", link: "/shirt" },
      { name: "Jacket", link: "/jacket" },
      { name: "Skirt", link: "/skirt" },
      { name: "Shorts", link: "/shorts" },
      { name: "Jeans", link: "/jeans" },
      { name: "Trouser", link: "/trouser" },
      { name: "Dress", link: "/dress" },
      { name: "Shoes", link: "/shoes"},
    ],
  },
  {
    name: "Men",
    items: [
      { name: "T-shirt", link: "/men/t-shirt" },
      { name: "Jeans", link: "/men/jeans" },
      { name: "Jacket", link: "/men/jacket" },
      { name: "Trouser", link: "/men/trouser" },
      { name: "Shirt", link: "/men/shirt" },
      { name: "Shoes", link: "/men/shoes"},
    ],
  },
  {
    name: "Girls",
    items: [
      { name: "Clothing", link: "/girls/clothing" },
      { name: "Shoes", link: "/girls/shoes"},
    ],
  },
  {
    name: "Boys",
    items: [
      { name: "Clothing", link: "/boys/clothing" },
      { name: "Shoes", link: "/boys/shoes"},
    ],
  },
  {
    name: "Accessories",
    items: [
      { name: "Glasses", link: "/accessories/glasses" },
      { name: "Watches", link: "/accessories/watches"},
      { name: "Gloves", link: "/accessories/gloves" },
      { name: "Belt", link: "/accessories/belt" },
      { name: "Hat", link: "/accessories/hat" },
      { name: "Bag", link: "/accessories/bag" },
      { name: "Wallet", link: "/accessories/wallet" },
    ],
  },
];



const Men_trouser = () => {
  const [favorites, setFavorites] = useState(new Set());
  const [openDropdown, setOpenDropdown] = useState(null);

  const toggleFavorite = (productId) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(productId)) {
      newFavorites.delete(productId);
    } else {
      newFavorites.add(productId);
    }
    setFavorites(newFavorites);
  };

  return (
    <div>
      <div className="homepage-container">
        <div className="header">
          <div className="logo-container">
            <Link to="/" className="logo-bg">
              <span className="logo-text">
                <span className="logo-star">★</span>
                StyleStore
              </span>
            </Link>
          </div>
          <div className="header-right">
            <div className="header-row">
              <input
                type="text"
                placeholder="Search"
                className="search-input"
              />
              <div className="icons-area">
                <Bell size={20} className="text-black" />
                <Heart size={20} className="text-black" />
                <div className="shopping-bag-rel">
                  <ShoppingBag size={22} className="text-black" />
                  <span className="shopping-bag-badge">0</span>
                </div>
                <Link to="/login" className="login-link">LOGIN</Link>
                <Link to="/register" className="register-link">REGISTER</Link>
              </div>
            </div>
          </div>
        </div>
        <div className="category-nav">
          {categories.map((cat) => (
            <div
              key={cat.name}
              className={`category-group${openDropdown === cat.name ? " open" : ""}`}
              onMouseEnter={() => cat.items && setOpenDropdown(cat.name)}
              onMouseLeave={() => cat.items && setOpenDropdown(null)}
            >
              <button
                className={`category-btn${cat.name === "Women" ? " women" : ""}`}
                style={{ fontWeight: 700 }}
                onClick={() => setOpenDropdown(cat.name === openDropdown ? null : cat.name)}
                type="button"
              >
                {cat.name}
              </button>
              {cat.items && (
                <div className="dropdown">
                  <ul>
                    {cat.items.map((item) => (
                      <li key={item.name}>
                        <Link to={item.link}>{item.name}</Link>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
        <div className="women-tshirt-container">
        <div className="sale-banner">
          <div className="banner-content">
            <div className="sale-text">
              <span className="up-to">UP TO</span>
              <span className="percentage">70% OFF</span>
              <span className="sale-subtitle">ON MID-YEAR SALE</span>
            </div>
          </div>
        </div>
        <div className="women-tshirt-title">Women T-shirt</div>

        {/* Product Grid */}
        <div className="products-grids">
          {products.map((product) => (
            <div key={product.id} className="product-cards">
              <div className="product-image-containers">
                <div className="discount-badge">{product.discount}%</div>
                <button
                  className={`favorite-btn ${favorites.has(product.id) ? 'active' : ''}`}
                  onClick={() => toggleFavorite(product.id)}
                >
                  <Heart size={16} fill={favorites.has(product.id) ? '#ef4444' : 'none'} />
                </button>
                <img src={product.image} alt={product.name} className="product-images" />
              </div>
              <div className="product-info">
                <h3 className="product-name">{product.name}</h3>
                <div className="price-container">
                  <span className="sale-price">US ${product.salePrice}</span>
                  <span className="original-price">US ${product.originalPrice}</span>
                </div>
                <div className="color-options">
                  {product.colors.map((color, index) => (
                    <div key={index} className={`color-dot ${color}`}></div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      </div>
      <div className="Footer"><Footer /></div>


      
    </div>
  );
};

export default Men_trouser;