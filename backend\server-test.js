const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const helmet = require('helmet');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Security middleware
app.use(helmet());

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Test routes without database
app.get('/', (req, res) => {
  res.json({ 
    message: 'StyleStore Backend API is running!',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'API is healthy',
    timestamp: new Date().toISOString()
  });
});

// Mock products endpoint for testing
app.get('/api/products', (req, res) => {
  const mockProducts = [
    {
      id: '1',
      name: 'Test T-Shirt',
      originalPrice: 39.99,
      salePrice: 27.99,
      discount: 30,
      mainImage: '/images/test.jpg',
      colors: ['white', 'black'],
      isOnSale: true,
      category: { name: 'T-Shirts', slug: 'women-t-shirt' }
    }
  ];
  
  res.json({
    success: true,
    data: {
      products: mockProducts,
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 1,
        itemsPerPage: 20
      }
    }
  });
});

// Mock categories endpoint
app.get('/api/categories', (req, res) => {
  const mockCategories = [
    {
      id: '1',
      name: 'Women T-shirt',
      slug: 'women-t-shirt',
      gender: 'women',
      type: 'clothing'
    }
  ];
  
  res.json({
    success: true,
    data: { categories: mockCategories }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Test Server is running on port ${PORT}`);
  console.log(`📱 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});
