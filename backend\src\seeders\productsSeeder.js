const { models } = require('../config');

const generateProductsData = (categories) => {
  const categoryMap = {};
  categories.forEach(cat => {
    categoryMap[cat.slug] = cat.id;
  });

  return [
    // Women T-shirts
    {
      name: "Regular Fitted Crop T-Shirt",
      originalPrice: 39.99,
      salePrice: 27.99,
      discount: 30,
      categoryId: categoryMap['women-t-shirt'],
      colors: ['white', 'red'],
      sizes: ['XS', 'S', 'M', 'L', 'XL'],
      stockQuantity: 50,
      mainImage: "/images/women_shirt42.jpg",
      images: ["/images/women_shirt42.jpg"],
      isOnSale: true,
      isFeatured: true,
      brand: "StyleStore",
      description: "Comfortable fitted crop t-shirt perfect for casual wear",
      tags: ["casual", "crop", "fitted"]
    },
    {
      name: "Regular Crop Textured T-Shirt",
      originalPrice: 44.99,
      salePrice: 31.49,
      discount: 30,
      categoryId: categoryMap['women-t-shirt'],
      colors: ['pink'],
      sizes: ['XS', 'S', 'M', 'L', 'XL'],
      stockQuantity: 45,
      mainImage: "/images/women_shirt39.jpg",
      images: ["/images/women_shirt39.jpg"],
      isOnSale: true,
      brand: "StyleStore",
      description: "Textured crop t-shirt with modern design",
      tags: ["casual", "crop", "textured"]
    },
    {
      name: "Regular V-Neck T-Shirt",
      originalPrice: 49.99,
      salePrice: 34.99,
      discount: 30,
      categoryId: categoryMap['women-t-shirt'],
      colors: ['beige'],
      sizes: ['XS', 'S', 'M', 'L', 'XL'],
      stockQuantity: 40,
      mainImage: "/images/women_shirt7.jpg",
      images: ["/images/women_shirt7.jpg"],
      isOnSale: true,
      brand: "StyleStore",
      description: "Classic v-neck t-shirt for everyday comfort",
      tags: ["casual", "v-neck", "classic"]
    },

    // Women Shirts
    {
      name: "Elegant Button-Up Shirt",
      originalPrice: 59.99,
      salePrice: 17.99,
      discount: 70,
      categoryId: categoryMap['women-shirt'],
      colors: ['white', 'blue'],
      sizes: ['XS', 'S', 'M', 'L', 'XL'],
      stockQuantity: 35,
      mainImage: "/images/women_shirt1.jpg",
      images: ["/images/women_shirt1.jpg"],
      isOnSale: true,
      isFeatured: true,
      brand: "StyleStore",
      description: "Professional button-up shirt for office wear",
      tags: ["formal", "button-up", "professional"]
    },
    {
      name: "Casual Cotton Shirt",
      originalPrice: 54.99,
      salePrice: 16.49,
      discount: 70,
      categoryId: categoryMap['women-shirt'],
      colors: ['pink', 'white'],
      sizes: ['XS', 'S', 'M', 'L', 'XL'],
      stockQuantity: 42,
      mainImage: "/images/women_shirt2.jpg",
      images: ["/images/women_shirt2.jpg"],
      isOnSale: true,
      brand: "StyleStore",
      description: "Soft cotton shirt for casual occasions",
      tags: ["casual", "cotton", "comfortable"]
    },

    // Women Jeans
    {
      name: "High-Waisted Skinny Jeans",
      originalPrice: 59.99,
      salePrice: 17.99,
      discount: 70,
      categoryId: categoryMap['women-jeans'],
      colors: ['blue'],
      sizes: ['24', '26', '28', '30', '32', '34'],
      stockQuantity: 30,
      mainImage: "/images/women_jean1.jpg",
      images: ["/images/women_jean1.jpg"],
      isOnSale: true,
      isFeatured: true,
      brand: "StyleStore",
      description: "Trendy high-waisted skinny jeans",
      tags: ["jeans", "skinny", "high-waisted"]
    },
    {
      name: "Ripped Boyfriend Jeans",
      originalPrice: 64.99,
      salePrice: 19.49,
      discount: 70,
      categoryId: categoryMap['women-jeans'],
      colors: ['light blue'],
      sizes: ['24', '26', '28', '30', '32', '34'],
      stockQuantity: 25,
      mainImage: "/images/women_jean2.jpg",
      images: ["/images/women_jean2.jpg"],
      isOnSale: true,
      brand: "StyleStore",
      description: "Relaxed fit boyfriend jeans with distressed details",
      tags: ["jeans", "boyfriend", "ripped"]
    },

    // Women Shoes
    {
      name: "Chunky Sneakers",
      originalPrice: 59.99,
      salePrice: 17.99,
      discount: 70,
      categoryId: categoryMap['women-shoes'],
      colors: ['white', 'pink'],
      sizes: ['5', '6', '7', '8', '9', '10'],
      stockQuantity: 20,
      mainImage: "/images/women_shoes1.jpg",
      images: ["/images/women_shoes1.jpg"],
      isOnSale: true,
      isFeatured: true,
      brand: "StyleStore",
      description: "Trendy chunky sneakers for casual wear",
      tags: ["sneakers", "chunky", "casual"]
    },
    {
      name: "Slip-On Flats",
      originalPrice: 49.99,
      salePrice: 14.99,
      discount: 70,
      categoryId: categoryMap['women-shoes'],
      colors: ['black'],
      sizes: ['5', '6', '7', '8', '9', '10'],
      stockQuantity: 28,
      mainImage: "/images/women_shoes2.jpg",
      images: ["/images/women_shoes2.jpg"],
      isOnSale: true,
      brand: "StyleStore",
      description: "Comfortable slip-on flats for everyday wear",
      tags: ["flats", "slip-on", "comfortable"]
    },

    // Men T-shirts
    {
      name: "Vintage Graphic Tee",
      originalPrice: 59.99,
      salePrice: 17.99,
      discount: 70,
      categoryId: categoryMap['men-t-shirt'],
      colors: ['black', 'grey'],
      sizes: ['S', 'M', 'L', 'XL', 'XXL'],
      stockQuantity: 35,
      mainImage: "/images/men_shirt1.jpg",
      images: ["/images/men_shirt1.jpg"],
      isOnSale: true,
      isFeatured: true,
      brand: "StyleStore",
      description: "Vintage-inspired graphic t-shirt",
      tags: ["graphic", "vintage", "casual"]
    },
    {
      name: "Modern Fit Tee",
      originalPrice: 49.99,
      salePrice: 14.99,
      discount: 70,
      categoryId: categoryMap['men-t-shirt'],
      colors: ['white'],
      sizes: ['S', 'M', 'L', 'XL', 'XXL'],
      stockQuantity: 40,
      mainImage: "/images/men_shirt2.jpg",
      images: ["/images/men_shirt2.jpg"],
      isOnSale: true,
      brand: "StyleStore",
      description: "Modern fit t-shirt for contemporary style",
      tags: ["modern", "fit", "casual"]
    },

    // Men Shoes
    {
      name: "Sport Sneakers",
      originalPrice: 59.99,
      salePrice: 17.99,
      discount: 70,
      categoryId: categoryMap['men-shoes'],
      colors: ['white', 'blue'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      stockQuantity: 25,
      mainImage: "/images/men_shoes1.jpg",
      images: ["/images/men_shoes1.jpg"],
      isOnSale: true,
      isFeatured: true,
      brand: "StyleStore",
      description: "Athletic sneakers for sports and casual wear",
      tags: ["sneakers", "sport", "athletic"]
    },
    {
      name: "Casual Loafers",
      originalPrice: 64.99,
      salePrice: 19.49,
      discount: 70,
      categoryId: categoryMap['men-shoes'],
      colors: ['brown'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      stockQuantity: 22,
      mainImage: "/images/men_shoes2.jpg",
      images: ["/images/men_shoes2.jpg"],
      isOnSale: true,
      brand: "StyleStore",
      description: "Comfortable loafers for casual occasions",
      tags: ["loafers", "casual", "comfortable"]
    },

    // Accessories
    {
      name: "Classic Sunglasses",
      originalPrice: 39.99,
      salePrice: 11.99,
      discount: 70,
      categoryId: categoryMap['glasses'],
      colors: ['black', 'brown'],
      sizes: ['One Size'],
      stockQuantity: 50,
      mainImage: "/images/glasses1.webp",
      images: ["/images/glasses1.webp"],
      isOnSale: true,
      brand: "StyleStore",
      description: "Classic sunglasses with UV protection",
      tags: ["sunglasses", "classic", "uv-protection"]
    },
    {
      name: "Digital Watch",
      originalPrice: 79.99,
      salePrice: 23.99,
      discount: 70,
      categoryId: categoryMap['watches'],
      colors: ['black', 'silver'],
      sizes: ['One Size'],
      stockQuantity: 30,
      mainImage: "/images/watches1.webp",
      images: ["/images/watches1.webp"],
      isOnSale: true,
      isFeatured: true,
      brand: "StyleStore",
      description: "Modern digital watch with multiple features",
      tags: ["digital", "watch", "modern"]
    }
  ];
};

const seedProducts = async (categories) => {
  try {
    console.log('🌱 Seeding products...');
    
    // Clear existing products
    await models.Product.destroy({ where: {} });
    
    // Generate products data
    const productsData = generateProductsData(categories);
    
    // Create products
    const products = await models.Product.bulkCreate(productsData);
    
    console.log(`✅ Successfully seeded ${products.length} products`);
    return products;
  } catch (error) {
    console.error('❌ Error seeding products:', error);
    throw error;
  }
};

module.exports = { seedProducts };
