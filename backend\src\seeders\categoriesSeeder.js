const { models } = require('../config');

const categoriesData = [
  // Women Categories
  {
    name: 'Women T-shirt',
    slug: 'women-t-shirt',
    gender: 'women',
    type: 'clothing',
    description: 'Stylish and comfortable t-shirts for women',
    sortOrder: 1
  },
  {
    name: 'Women Shirt',
    slug: 'women-shirt',
    gender: 'women',
    type: 'clothing',
    description: 'Elegant shirts for women',
    sortOrder: 2
  },
  {
    name: 'Women Jacket',
    slug: 'women-jacket',
    gender: 'women',
    type: 'clothing',
    description: 'Fashionable jackets for women',
    sortOrder: 3
  },
  {
    name: 'Women Skirt',
    slug: 'women-skirt',
    gender: 'women',
    type: 'clothing',
    description: 'Beautiful skirts for women',
    sortOrder: 4
  },
  {
    name: 'Women Shorts',
    slug: 'women-shorts',
    gender: 'women',
    type: 'clothing',
    description: 'Comfortable shorts for women',
    sortOrder: 5
  },
  {
    name: 'Women Jeans',
    slug: 'women-jeans',
    gender: 'women',
    type: 'clothing',
    description: 'Trendy jeans for women',
    sortOrder: 6
  },
  {
    name: 'Women Trouser',
    slug: 'women-trouser',
    gender: 'women',
    type: 'clothing',
    description: 'Professional trousers for women',
    sortOrder: 7
  },
  {
    name: 'Women Dress',
    slug: 'women-dress',
    gender: 'women',
    type: 'clothing',
    description: 'Elegant dresses for women',
    sortOrder: 8
  },
  {
    name: 'Women Shoes',
    slug: 'women-shoes',
    gender: 'women',
    type: 'shoes',
    description: 'Stylish shoes for women',
    sortOrder: 9
  },

  // Men Categories
  {
    name: 'Men T-shirt',
    slug: 'men-t-shirt',
    gender: 'men',
    type: 'clothing',
    description: 'Comfortable t-shirts for men',
    sortOrder: 10
  },
  {
    name: 'Men Jeans',
    slug: 'men-jeans',
    gender: 'men',
    type: 'clothing',
    description: 'Durable jeans for men',
    sortOrder: 11
  },
  {
    name: 'Men Jacket',
    slug: 'men-jacket',
    gender: 'men',
    type: 'clothing',
    description: 'Stylish jackets for men',
    sortOrder: 12
  },
  {
    name: 'Men Trouser',
    slug: 'men-trouser',
    gender: 'men',
    type: 'clothing',
    description: 'Professional trousers for men',
    sortOrder: 13
  },
  {
    name: 'Men Shirt',
    slug: 'men-shirt',
    gender: 'men',
    type: 'clothing',
    description: 'Classic shirts for men',
    sortOrder: 14
  },
  {
    name: 'Men Shoes',
    slug: 'men-shoes',
    gender: 'men',
    type: 'shoes',
    description: 'Quality shoes for men',
    sortOrder: 15
  },

  // Girls Categories
  {
    name: 'Girls Clothing',
    slug: 'girls-clothing',
    gender: 'girls',
    type: 'clothing',
    description: 'Cute clothing for girls',
    sortOrder: 16
  },
  {
    name: 'Girls Shoes',
    slug: 'girls-shoes',
    gender: 'girls',
    type: 'shoes',
    description: 'Comfortable shoes for girls',
    sortOrder: 17
  },

  // Boys Categories
  {
    name: 'Boys Clothing',
    slug: 'boys-clothing',
    gender: 'boys',
    type: 'clothing',
    description: 'Trendy clothing for boys',
    sortOrder: 18
  },
  {
    name: 'Boys Shoes',
    slug: 'boys-shoes',
    gender: 'boys',
    type: 'shoes',
    description: 'Durable shoes for boys',
    sortOrder: 19
  },

  // Accessories Categories
  {
    name: 'Glasses',
    slug: 'glasses',
    gender: 'unisex',
    type: 'accessories',
    description: 'Stylish glasses and sunglasses',
    sortOrder: 20
  },
  {
    name: 'Watches',
    slug: 'watches',
    gender: 'unisex',
    type: 'accessories',
    description: 'Elegant watches',
    sortOrder: 21
  },
  {
    name: 'Gloves',
    slug: 'gloves',
    gender: 'unisex',
    type: 'accessories',
    description: 'Warm and comfortable gloves',
    sortOrder: 22
  },
  {
    name: 'Belt',
    slug: 'belt',
    gender: 'unisex',
    type: 'accessories',
    description: 'Quality belts',
    sortOrder: 23
  },
  {
    name: 'Hat',
    slug: 'hat',
    gender: 'unisex',
    type: 'accessories',
    description: 'Fashionable hats',
    sortOrder: 24
  },
  {
    name: 'Bag',
    slug: 'bag',
    gender: 'unisex',
    type: 'accessories',
    description: 'Stylish bags and backpacks',
    sortOrder: 25
  },
  {
    name: 'Wallet',
    slug: 'wallet',
    gender: 'unisex',
    type: 'accessories',
    description: 'Premium wallets',
    sortOrder: 26
  }
];

const seedCategories = async () => {
  try {
    console.log('🌱 Seeding categories...');
    
    // Clear existing categories
    await models.Category.destroy({ where: {} });
    
    // Create categories
    const categories = await models.Category.bulkCreate(categoriesData);
    
    console.log(`✅ Successfully seeded ${categories.length} categories`);
    return categories;
  } catch (error) {
    console.error('❌ Error seeding categories:', error);
    throw error;
  }
};

module.exports = { seedCategories, categoriesData };
