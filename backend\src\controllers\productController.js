const { models } = require('../config');
const { Op } = require('sequelize');

const getAllProducts = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      gender,
      minPrice,
      maxPrice,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      isOnSale,
      isFeatured
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { isActive: true };

    // Apply filters
    if (category) {
      const categoryRecord = await models.Category.findOne({
        where: { slug: category }
      });
      if (categoryRecord) {
        where.categoryId = categoryRecord.id;
      }
    }

    if (gender) {
      const categoryRecord = await models.Category.findOne({
        where: { gender }
      });
      if (categoryRecord) {
        where.categoryId = categoryRecord.id;
      }
    }

    if (minPrice || maxPrice) {
      where.originalPrice = {};
      if (minPrice) where.originalPrice[Op.gte] = minPrice;
      if (maxPrice) where.originalPrice[Op.lte] = maxPrice;
    }

    if (search) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
        { brand: { [Op.iLike]: `%${search}%` } }
      ];
    }

    if (isOnSale === 'true') {
      where.isOnSale = true;
    }

    if (isFeatured === 'true') {
      where.isFeatured = true;
    }

    const { count, rows: products } = await models.Product.findAndCountAll({
      where,
      include: [
        {
          model: models.Category,
          as: 'category',
          attributes: ['id', 'name', 'slug', 'gender', 'type']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy, sortOrder.toUpperCase()]],
      distinct: true
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: count,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get products',
      error: error.message
    });
  }
};

const getProductById = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await models.Product.findOne({
      where: { id, isActive: true },
      include: [
        {
          model: models.Category,
          as: 'category',
          attributes: ['id', 'name', 'slug', 'gender', 'type']
        }
      ]
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Increment view count
    await product.increment('viewCount');

    res.json({
      success: true,
      data: { product }
    });
  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get product',
      error: error.message
    });
  }
};

const getProductBySlug = async (req, res) => {
  try {
    const { slug } = req.params;

    const product = await models.Product.findOne({
      where: { slug, isActive: true },
      include: [
        {
          model: models.Category,
          as: 'category',
          attributes: ['id', 'name', 'slug', 'gender', 'type']
        }
      ]
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Increment view count
    await product.increment('viewCount');

    res.json({
      success: true,
      data: { product }
    });
  } catch (error) {
    console.error('Get product by slug error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get product',
      error: error.message
    });
  }
};

const getFeaturedProducts = async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    const products = await models.Product.findAll({
      where: { isActive: true, isFeatured: true },
      include: [
        {
          model: models.Category,
          as: 'category',
          attributes: ['id', 'name', 'slug', 'gender', 'type']
        }
      ],
      limit: parseInt(limit),
      order: [['sortOrder', 'ASC'], ['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: { products }
    });
  } catch (error) {
    console.error('Get featured products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get featured products',
      error: error.message
    });
  }
};

const getSaleProducts = async (req, res) => {
  try {
    const { limit = 20, page = 1 } = req.query;
    const offset = (page - 1) * limit;

    const { count, rows: products } = await models.Product.findAndCountAll({
      where: { isActive: true, isOnSale: true },
      include: [
        {
          model: models.Category,
          as: 'category',
          attributes: ['id', 'name', 'slug', 'gender', 'type']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['discount', 'DESC'], ['createdAt', 'DESC']],
      distinct: true
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get sale products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get sale products',
      error: error.message
    });
  }
};

module.exports = {
  getAllProducts,
  getProductById,
  getProductBySlug,
  getFeaturedProducts,
  getSaleProducts
};
