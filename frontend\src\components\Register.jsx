import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import '../styles/Register.css';


const Register = () => {
  const [formData, setFormData] = useState({
    gender: '',
    firstName: '',
    lastName: '',
    telephone: '',
    email: '',
    password: '',
    confirmPassword: '',
    country: 'Cambodia',
    city: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { register } = useAuth();
  const navigate = useNavigate();

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleGenderChange = (gender) => {
    setFormData(prev => ({
      ...prev,
      gender: gender
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setLoading(true);

    try {
      const result = await register(formData);
      if (result.success) {
        navigate('/'); // Redirect to home page on successful registration
      } else {
        setError(result.message || 'Registration failed');
      }
    } catch (err) {
      setError('An error occurred during registration');
    } finally {
      setLoading(false);
    }
  };

  const handleFacebookLogin = () => {
    console.log('Facebook registration clicked');
    // Add Facebook OAuth logic here
  };

  return (
    <div className="register-container">
      <div className="register-card">
        <form onSubmit={handleSubmit} className="register-form">
          {error && (
            <div className="error-message" style={{
              color: '#ef4444',
              marginBottom: '1rem',
              padding: '0.5rem',
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '4px'
            }}>
              {error}
            </div>
          )}
          {/* Gender Selection */}
          <div className="gender-group">
            <label className="gender-label">
              Gender <span className="required">*</span>
            </label>
            <div className="gender-options">
              <button
                type="button"
                className={`gender-btn ${formData.gender === 'male' ? 'active' : ''}`}
                onClick={() => handleGenderChange('male')}
              >
                Male
              </button>
              <button
                type="button"
                className={`gender-btn ${formData.gender === 'female' ? 'active' : ''}`}
                onClick={() => handleGenderChange('female')}
              >
                Female
              </button>
            </div>
          </div>

          {/* Name Fields */}
          <div className="name-row">
            <div className="input-group">
              <label htmlFor="firstName" className="input-label">
                First name
              </label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                placeholder="Enter first name"
                className="form-input"
                required
              />
            </div>
            <div className="input-group">
              <label htmlFor="lastName" className="input-label">
                Last name
              </label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                placeholder="Enter last name"
                className="form-input"
                required
              />
            </div>
          </div>

          {/* Mobile Number */}
          <div className="input-group">
            <label htmlFor="telephone" className="input-label">
              Mobile number
            </label>
            <input
              type="tel"
              id="telephone"
              name="telephone"
              value={formData.telephone}
              onChange={handleInputChange}
              placeholder="Enter phone number"
              className="form-input"
              required
            />
          </div>

          {/* Email */}
          <div className="input-group">
            <label htmlFor="email" className="input-label">
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="Enter email"
              className="form-input"
              required
            />
          </div>

          {/* Password */}
          <div className="input-group">
            <label htmlFor="password" className="input-label">
              Password <span className="required">*</span>
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              placeholder="Enter password"
              className="form-input"
              required
            />
          </div>

          {/* Confirm Password */}
          <div className="input-group">
            <label htmlFor="confirmPassword" className="input-label">
              Confirm Password <span className="required">*</span>
            </label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              placeholder="Confirm password"
              className="form-input"
              required
            />
          </div>

          {/* Location Fields */}
          <div className="location-row">
            <div className="input-group">
              <select
                name="country"
                value={formData.country}
                onChange={handleInputChange}
                className="form-select"
              >
                <option value="Cambodia">Cambodia</option>
                <option value="Thailand">Thailand</option>
                <option value="Vietnam">Vietnam</option>
                <option value="Malaysia">Malaysia</option>
              </select>
            </div>
            <div className="input-group">
              <select
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                className="form-select"
              >
                <option value="">City/province</option>
                <option value="Phnom Penh">Phnom Penh</option>
                <option value="Siem Reap">Siem Reap</option>
                <option value="Battambang">Battambang</option>
                <option value="Kampong Cham">Kampong Cham</option>
              </select>
            </div>
          </div>

          {/* Create Account Button */}
          <button type="submit" className="create-account-button">
            CREATE ACCOUNT
          </button>

          {/* Divider */}
          <div className="divider">
            <span>OR</span>
          </div>

          {/* Facebook Login */}
          <button
            type="button"
            className="facebook-button"
            onClick={handleFacebookLogin}
          >
            <span className="facebook-icon">f</span>
            Continue with Facebook
          </button>

          {/* Login Link */}
          <div className="login-section">
            <span className="login-text">
              Already have an account? <a href="/login" className="login-link">Login</a>
            </span>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Register;