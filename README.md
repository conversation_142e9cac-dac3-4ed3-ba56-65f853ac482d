# StyleStore - Clothing E-commerce Application

A full-stack clothing e-commerce application with React frontend and Node.js/Express backend, using PostgreSQL database hosted on Aiven.

## Project Structure

```
PROJECT_CLOTHING/
├── backend/                 # Node.js/Express backend
│   ├── src/
│   │   ├── config/         # Database configuration
│   │   ├── controllers/    # Route controllers
│   │   ├── middleware/     # Custom middleware
│   │   ├── models/         # Sequelize models
│   │   ├── routes/         # API routes
│   │   ├── seeders/        # Database seeders
│   │   └── utils/          # Utility functions
│   ├── ca.pem             # SSL certificate for Aiven
│   ├── server.js          # Main server file
│   └── package.json
└── frontend/               # React frontend
    ├── src/
    │   ├── components/     # React components
    │   ├── contexts/       # React contexts
    │   ├── hooks/          # Custom hooks
    │   ├── styles/         # CSS files
    │   └── utils/          # Utility functions
    └── package.json
```

## Features

### Backend
- **Authentication**: JWT-based authentication with bcrypt password hashing
- **Database**: PostgreSQL with Sequelize ORM
- **Security**: Helmet, CORS, rate limiting
- **API**: RESTful API with proper error handling
- **Models**: User, Product, Category, Order, Cart management

### Frontend
- **Framework**: React with Vite
- **Routing**: React Router for navigation
- **State Management**: Context API for authentication
- **UI**: Responsive design with custom CSS
- **API Integration**: Custom hooks for data fetching

## Setup Instructions

### Prerequisites
- Node.js (v16 or higher)
- PostgreSQL database (Aiven account recommended)
- Git

### Backend Setup

1. **Navigate to backend directory:**
   ```bash
   cd backend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Configure environment variables:**
   - Copy `.env.example` to `.env`
   - Update the database credentials with your Aiven PostgreSQL details:
   ```env
   DB_HOST=your-aiven-host.aivencloud.com
   DB_PORT=your-port
   DB_NAME=your-database-name
   DB_USER=your-username
   DB_PASSWORD=your-password
   JWT_SECRET=your-super-secret-jwt-key-here
   ```

4. **Ensure SSL certificate:**
   - The `ca.pem` file should be in the backend directory
   - This is required for Aiven PostgreSQL SSL connection

5. **Run database seeders:**
   ```bash
   npm run seed
   ```

6. **Start the backend server:**
   ```bash
   npm run dev
   ```
   The backend will run on `http://localhost:5000`

### Frontend Setup

1. **Navigate to frontend directory:**
   ```bash
   cd frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Configure environment variables:**
   - Copy `.env.example` to `.env`
   - Update the API URL if needed:
   ```env
   VITE_API_URL=http://localhost:5000/api
   ```

4. **Start the frontend development server:**
   ```bash
   npm run dev
   ```
   The frontend will run on `http://localhost:5173`

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile (protected)
- `PUT /api/auth/profile` - Update user profile (protected)
- `PUT /api/auth/change-password` - Change password (protected)

### Products
- `GET /api/products` - Get all products with filtering
- `GET /api/products/:id` - Get product by ID
- `GET /api/products/slug/:slug` - Get product by slug
- `GET /api/products/featured` - Get featured products
- `GET /api/products/sale` - Get sale products

### Categories
- `GET /api/categories` - Get all categories
- `GET /api/categories/:id` - Get category by ID
- `GET /api/categories/slug/:slug` - Get category by slug
- `GET /api/categories/tree` - Get category tree

## Database Schema

### Users
- Authentication and profile information
- Encrypted passwords with bcrypt
- Role-based access (customer/admin)

### Products
- Product details, pricing, inventory
- Category relationships
- Image and variant management

### Categories
- Hierarchical category structure
- Gender and type classification

### Orders & Cart
- Order management system
- Shopping cart functionality

## Development

### Backend Development
```bash
cd backend
npm run dev  # Starts with nodemon for auto-reload
```

### Frontend Development
```bash
cd frontend
npm run dev  # Starts Vite dev server with hot reload
```

### Database Management
```bash
cd backend
npm run seed  # Run database seeders
```

## Production Deployment

1. **Backend:**
   - Set `NODE_ENV=production`
   - Use process manager like PM2
   - Configure reverse proxy (nginx)

2. **Frontend:**
   - Build production bundle: `npm run build`
   - Serve static files from `dist/` directory

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
