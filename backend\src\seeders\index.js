const { initializeDatabase } = require('../config');
const { seedCategories } = require('./categoriesSeeder');
const { seedProducts } = require('./productsSeeder');

const runSeeders = async () => {
  try {
    console.log('🚀 Starting database seeding...');
    
    // Initialize database connection
    await initializeDatabase();
    
    // Seed categories first
    const categories = await seedCategories();
    
    // Seed products (depends on categories)
    await seedProducts(categories);
    
    console.log('🎉 Database seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  }
};

// Run seeders if this file is executed directly
if (require.main === module) {
  runSeeders();
}

module.exports = { runSeeders };
