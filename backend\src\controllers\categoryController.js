const { models } = require('../config');

const getAllCategories = async (req, res) => {
  try {
    const { type, gender, includeProducts = false } = req.query;

    const where = { isActive: true };
    
    if (type) {
      where.type = type;
    }
    
    if (gender) {
      where.gender = gender;
    }

    const include = [];
    
    if (includeProducts === 'true') {
      include.push({
        model: models.Product,
        as: 'products',
        where: { isActive: true },
        required: false,
        attributes: ['id', 'name', 'slug', 'originalPrice', 'salePrice', 'mainImage', 'isOnSale']
      });
    }

    // Include parent and children categories
    include.push(
      {
        model: models.Category,
        as: 'parent',
        attributes: ['id', 'name', 'slug']
      },
      {
        model: models.Category,
        as: 'children',
        where: { isActive: true },
        required: false,
        attributes: ['id', 'name', 'slug', 'type', 'gender']
      }
    );

    const categories = await models.Category.findAll({
      where,
      include,
      order: [['sortOrder', 'ASC'], ['name', 'ASC']]
    });

    res.json({
      success: true,
      data: { categories }
    });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get categories',
      error: error.message
    });
  }
};

const getCategoryById = async (req, res) => {
  try {
    const { id } = req.params;

    const category = await models.Category.findOne({
      where: { id, isActive: true },
      include: [
        {
          model: models.Category,
          as: 'parent',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: models.Category,
          as: 'children',
          where: { isActive: true },
          required: false,
          attributes: ['id', 'name', 'slug', 'type', 'gender']
        },
        {
          model: models.Product,
          as: 'products',
          where: { isActive: true },
          required: false,
          attributes: ['id', 'name', 'slug', 'originalPrice', 'salePrice', 'mainImage', 'isOnSale'],
          limit: 20,
          order: [['sortOrder', 'ASC'], ['createdAt', 'DESC']]
        }
      ]
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    res.json({
      success: true,
      data: { category }
    });
  } catch (error) {
    console.error('Get category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get category',
      error: error.message
    });
  }
};

const getCategoryBySlug = async (req, res) => {
  try {
    const { slug } = req.params;

    const category = await models.Category.findOne({
      where: { slug, isActive: true },
      include: [
        {
          model: models.Category,
          as: 'parent',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: models.Category,
          as: 'children',
          where: { isActive: true },
          required: false,
          attributes: ['id', 'name', 'slug', 'type', 'gender']
        }
      ]
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    res.json({
      success: true,
      data: { category }
    });
  } catch (error) {
    console.error('Get category by slug error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get category',
      error: error.message
    });
  }
};

const getCategoryTree = async (req, res) => {
  try {
    const categories = await models.Category.findAll({
      where: { isActive: true, parentId: null },
      include: [
        {
          model: models.Category,
          as: 'children',
          where: { isActive: true },
          required: false,
          include: [
            {
              model: models.Category,
              as: 'children',
              where: { isActive: true },
              required: false
            }
          ]
        }
      ],
      order: [
        ['sortOrder', 'ASC'],
        ['name', 'ASC'],
        [{ model: models.Category, as: 'children' }, 'sortOrder', 'ASC'],
        [{ model: models.Category, as: 'children' }, 'name', 'ASC']
      ]
    });

    res.json({
      success: true,
      data: { categories }
    });
  } catch (error) {
    console.error('Get category tree error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get category tree',
      error: error.message
    });
  }
};

module.exports = {
  getAllCategories,
  getCategoryById,
  getCategoryBySlug,
  getCategoryTree
};
