const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const OrderItem = sequelize.define('OrderItem', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  orderId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'orders',
      key: 'id'
    }
  },
  productId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  productName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  productSku: {
    type: DataTypes.STRING,
    allowNull: false
  },
  productImage: {
    type: DataTypes.STRING,
    allowNull: true
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: 1
    }
  },
  unitPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  totalPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  selectedColor: {
    type: DataTypes.STRING,
    allowNull: true
  },
  selectedSize: {
    type: DataTypes.STRING,
    allowNull: true
  },
  productVariant: {
    type: DataTypes.JSON,
    allowNull: true
  }
}, {
  tableName: 'order_items',
  timestamps: true,
  hooks: {
    beforeCreate: (orderItem) => {
      orderItem.totalPrice = orderItem.quantity * orderItem.unitPrice;
    },
    beforeUpdate: (orderItem) => {
      if (orderItem.changed('quantity') || orderItem.changed('unitPrice')) {
        orderItem.totalPrice = orderItem.quantity * orderItem.unitPrice;
      }
    }
  }
});

module.exports = OrderItem;
